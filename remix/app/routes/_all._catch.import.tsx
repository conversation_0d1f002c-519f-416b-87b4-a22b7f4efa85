import { useMemo, useState } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { entries } from "~/misc/helpers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { LoaderFunctionArgs } from "@remix-run/router";
import { createPageOverwrites } from "~/misc/consts";
import { Kysely } from "kysely";
import { DB } from "~/kysely/db";
import { FakePostgresDialect, generateSQL } from "~/misc/kysely-fake-driver";

export { action } from "~/routes/_all._catch.resource";

const kyselyClient = new Kysely<DB>({
  dialect: new FakePostgresDialect(),
});

export const loader = async ({ request }: LoaderFunctionArgs) => {
  return {
    ...createPageOverwrites({ fixed_width: true }),
  };
};

// Parse a CSV line with semicolon delimiter and quote escaping
const parseLine = (line: string): string[] => {
  const result: string[] = [];
  let current = "";
  let inQuotes = false;
  let i = 0;

  while (i < line.length) {
    const char = line[i];

    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // Escaped quote (double quote)
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === ";" && !inQuotes) {
      // Field separator
      result.push(current);
      current = "";
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // Add the last field
  result.push(current);
  return result;
};

function csvToJson(csvText: string): Record<string, string>[] {
  const lines = csvText.trim().split("\n");
  if (lines.length < 2) return [];

  // Parse header row to get column names
  const headers = parseLine(lines[0]);

  // Parse data rows and create objects
  const data: Record<string, string | null>[] = [];
  for (let i = 1; i < lines.length; i++) {
    const values = parseLine(lines[i]);
    const obj: Record<string, string> = {};

    headers.forEach((header, index) => {
      obj[header] = values[index] || null;
    });

    data.push(obj);
  }

  return data;
}

const segmentKeys = ["csv", "json", "sql", "columns"] as const;
type SegmentKey = (typeof segmentKeys)[number];

const generateInsertSQL = async (inserts: any) => {
  try {
    const query = kyselyClient.insertInto("bla" as any).values(inserts as any);

    const { sql, parameters } = await generateSQL(query);
    return `-- Generated INSERT SQL\n${sql}\n\n-- Parameters: ${JSON.stringify(parameters)}`;
  } catch (error) {
    return `-- Error generating SQL: ${error}`;
  }
};

const Uploaded = (props: { file: string }) => {
  const search = useSearchParams2();
  const file = props.file;

  const converted = useMemo(() => {
    return csvToJson(props.file);
  }, [props.file]);
  const generatedSQL = useMemo(() => {
    return generateInsertSQL(converted);
  }, [converted]);

  const segments = {
    csv: {
      title: "CSV",
      body: <pre>{file}</pre>,
    },
    json: {
      title: "JSON",
      body: <pre>{JSON.stringify(converted, null, 2)}</pre>,
    },
    columns: {
      title: "Columns",
      body: (
        <div>
          {/*<pre>{converted?.[0] ? Object.keys(converted[0]).join(", ") : "No data"}</pre>*/}
        </div>
      ),
    },
    sql: {
      title: "SQL",
      body: (
        <div>
          <pre>{generatedSQL}</pre>
        </div>
      ),
    },
  } satisfies Record<SegmentKey, any>;
  // Convert CSV text to JSON array of objects
  const activeSegmentKey = segmentKeys.find((key) => key === search.state.tab) || ("csv" satisfies SegmentKey);
  const activeSegment = segments[activeSegmentKey];

  return (
    <div>
      <div className="flex flex-wrap gap-3 py-2">
        {entries(segments).map(([key, value]) => {
          return (
            <ParamLink
              key={key}
              paramState={{ tab: key }}
              aria-selected={activeSegmentKey === key}
              className="btn btn-basic aria-selected:btn-secondary"
            >
              {value.title}
            </ParamLink>
          );
        })}
      </div>
      <div className="overflow-auto">{activeSegment.body}</div>
    </div>
  );
};

const Body = () => {
  const [file, setFile] = useState<string | null>(null);
  // const [jsonData, setJsonData] = useState<Record<string, string>[] | null>(null);

  if (!file)
    return (
      <div>
        <input
          type={"file"}
          onChange={(e) => {
            console.log("fiel", e.target.files);
            //   get file contents
            const file = e.target.files?.[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (event) => {
              const text = event.target?.result;
              if (typeof text === "string") {
                setFile(text);
              }
            };
            reader.readAsText(file);
          }}
        />
      </div>
    );

  return <Uploaded file={file} />;
};

export default function Page() {
  return (
    <div className="px-6">
      <h1 className="font-semibold text-xl">Import</h1>
      <Body />
    </div>
  );
}
