import {
  DatabaseConnection,
  Driver,
  QueryResult,
  TransactionSettings,
  CompiledQuery,
} from 'kysely';

/**
 * A fake PostgreSQL driver for Kysely that can be used on the client-side
 * to generate SQL queries without actually connecting to a database.
 * 
 * This is useful when you want to use <PERSON><PERSON><PERSON>'s query builder to generate
 * SQL strings for display or other purposes without needing a real database connection.
 */
export class FakePostgresDriver implements Driver {
  async init(): Promise<void> {
    // No initialization needed for fake driver
  }

  async acquireConnection(): Promise<FakePostgresConnection> {
    return new FakePostgresConnection();
  }

  async beginTransaction(
    connection: FakePostgresConnection,
    settings: TransactionSettings
  ): Promise<void> {
    // No-op for fake driver
  }

  async commitTransaction(connection: FakePostgresConnection): Promise<void> {
    // No-op for fake driver
  }

  async rollbackTransaction(connection: FakePostgresConnection): Promise<void> {
    // No-op for fake driver
  }

  async releaseConnection(connection: FakePostgresConnection): Promise<void> {
    // No-op for fake driver
  }

  async destroy(): Promise<void> {
    // No-op for fake driver
  }
}

class FakePostgresConnection implements DatabaseConnection {
  async executeQuery<O>(compiledQuery: CompiledQuery): Promise<QueryResult<O>> {
    // For a fake driver, we don't actually execute the query
    // We just return an empty result set
    // The main purpose is to allow SQL generation via compiledQuery.sql
    
    console.log('Generated SQL:', compiledQuery.sql);
    console.log('Parameters:', compiledQuery.parameters);
    
    return {
      rows: [] as O[],
      numAffectedRows: BigInt(0),
      insertId: undefined,
      numChangedRows: undefined,
    };
  }

  async *streamQuery<O>(
    compiledQuery: CompiledQuery,
    chunkSize?: number
  ): AsyncIterableIterator<QueryResult<O>> {
    // For streaming, just yield an empty result
    yield {
      rows: [] as O[],
      numAffectedRows: BigInt(0),
      insertId: undefined,
      numChangedRows: undefined,
    };
  }
}

/**
 * A fake PostgreSQL dialect that uses the FakePostgresDriver
 */
export class FakePostgresDialect {
  constructor() {}

  createDriver(): Driver {
    return new FakePostgresDriver();
  }

  createQueryCompiler() {
    // Import the actual PostgreSQL query compiler from Kysely
    const { PostgresQueryCompiler } = require('kysely');
    return new PostgresQueryCompiler();
  }

  createAdapter() {
    // Import the actual PostgreSQL adapter from Kysely
    const { PostgresAdapter } = require('kysely');
    return new PostgresAdapter();
  }

  createIntrospector(db: any) {
    // Import the actual PostgreSQL introspector from Kysely
    const { PostgresIntrospector } = require('kysely');
    return new PostgresIntrospector(db);
  }
}
