import { Kysely } from "kysely";
import { DB } from "~/kysely/db";
import { FakePostgresDialect, generateSQL } from "~/misc/kysely-fake-driver";

// Create a Kysely instance with the fake driver
const fakeKysely = new Kysely<DB>({
  dialect: new FakePostgresDialect()
});

// Example usage: Generate SQL for various queries
export async function exampleUsage() {
  
  // Example 1: Simple SELECT query
  const selectQuery = fakeKysely
    .selectFrom('user')
    .select(['id', 'email', 'name'])
    .where('email', 'like', '%@example.com')
    .orderBy('name', 'asc')
    .limit(10);

  const selectSQL = await generateSQL(selectQuery);
  console.log('SELECT SQL:', selectSQL.sql);
  console.log('Parameters:', selectSQL.parameters);

  // Example 2: INSERT query
  const insertQuery = fakeKysely
    .insertInto('user')
    .values({
      email: '<EMAIL>',
      name: 'Test User',
      created_at: new Date().toISOString()
    });

  const insertSQL = await generateSQL(insertQuery);
  console.log('INSERT SQL:', insertSQL.sql);
  console.log('Parameters:', insertSQL.parameters);

  // Example 3: UPDATE query
  const updateQuery = fakeKysely
    .updateTable('user')
    .set({ name: 'Updated Name' })
    .where('id', '=', 123);

  const updateSQL = await generateSQL(updateQuery);
  console.log('UPDATE SQL:', updateSQL.sql);
  console.log('Parameters:', updateSQL.parameters);

  // Example 4: Complex JOIN query
  const joinQuery = fakeKysely
    .selectFrom('booking')
    .innerJoin('user', 'user.id', 'booking.user_id')
    .innerJoin('product', 'product.id', 'booking.product_id')
    .select([
      'booking.id as booking_id',
      'user.name as user_name',
      'product.name as product_name',
      'booking.created_at'
    ])
    .where('booking.status', '=', 'confirmed')
    .orderBy('booking.created_at', 'desc');

  const joinSQL = await generateSQL(joinQuery);
  console.log('JOIN SQL:', joinSQL.sql);
  console.log('Parameters:', joinSQL.parameters);

  return {
    selectSQL,
    insertSQL,
    updateSQL,
    joinSQL
  };
}

// Helper function to generate INSERT SQL from CSV data
export async function generateInsertSQLFromData(
  tableName: keyof DB,
  data: Record<string, any>[]
): Promise<{ sql: string; parameters: readonly unknown[] }> {
  if (!data || data.length === 0) {
    throw new Error('No data provided');
  }

  const query = fakeKysely
    .insertInto(tableName)
    .values(data as any);

  return await generateSQL(query);
}

// Helper function to generate CREATE TABLE SQL (basic version)
export function generateCreateTableSQL(
  tableName: string,
  columns: Record<string, string>
): string {
  const columnDefinitions = Object.entries(columns)
    .map(([name, type]) => `  ${name} ${type}`)
    .join(',\n');

  return `CREATE TABLE ${tableName} (\n${columnDefinitions}\n);`;
}

// Export the fake kysely instance for use in other files
export { fakeKysely };
