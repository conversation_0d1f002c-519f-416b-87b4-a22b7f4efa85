import type { DB } from "~/kysely/db";
import { FieldType } from "~/misc/formdata-to-nested-json";
import { localHost } from "~/misc/vars";
import { difference, flat, unique } from "remeda";

export const safeParseJson = (value: unknown, fallback: {} | [] = {}) => {
  try {
    return JSON.parse((value as string) || "{}");
  } catch (e) {
    console.log("could not parse json string", e);
    return fallback;
  }
};

export function myGroupBy<T>(items: T[], groupBy: (item: T) => string) {
  const groups = new Set<string>([]);
  return items
    .filter((item) => {
      if (groups.has(groupBy(item))) {
        return false;
      } else {
        groups.add(groupBy(item));
        return true;
      }
    })
    .map((group) => ({
      ...group,
      items: items.filter((item) => groupBy(item) === groupBy(group)),
    }));
}

type NonEmptyArray<T> = [T, ...T[]];

export function myGroupBy2<T>(items: T[], groupBy: (item: T) => string) {
  const groups = new Set<string>([]);
  return items
    .filter((item) => {
      if (groups.has(groupBy(item))) {
        return false;
      } else {
        groups.add(groupBy(item));
        return true;
      }
    })
    .map((group) => {
      const groupKey = groupBy(group);
      const itemsInGroup = items.filter((item) => groupBy(item) === groupKey);
      return {
        groupKey: groupKey,
        items: itemsInGroup as NonEmptyArray<T>,
      };
    });
}

export const bgUrl = (url: string) => `url("${url}")`;

export const entries = <Key extends string, Value>(obj: Partial<Record<Key, Value>>) =>
  Object.entries(obj).map((entry) => entry as [Key, Value]);

export const keys = <Key extends string>(obj: Record<Key, any> | object) => Object.keys(obj) as unknown as ReadonlyArray<Key>;

export const toArray = <Key extends string, Value extends object>(obj: Record<Key, Value>) =>
  entries(obj).map(([key, value]) => ({ key: key, ...value }));

export const slugify = (str: string) => {
  const a = "àáâäæãåāăąçćčđďèéêëēėęěğǵḧîïíīįìıİłḿñńǹňôöòóœøōõőṕŕřßśšşșťțûüùúūǘůűųẃẍÿýžźż·/_,:;";
  const b = "aaaaaaaaaacccddeeeeeeeegghiiiiiiiilmnnnnoooooooooprrsssssttuuuuuuuuuwxyyzzz------";
  const p = new RegExp(a.split("").join("|"), "g");

  return str
    .toString()
    .toLowerCase()
    .replace(/\s+/g, "-") // Replace spaces with -
    .replace(p, (c) => b.charAt(a.indexOf(c))) // Replace special characters
    .replace(/&/g, "-and-") // Replace & with 'and'
    .replace(/[^\w\-]+/g, "") // Remove all non-word characters
    .replace(/\-\-+/g, "-") // Replace multiple - with single -
    .replace(/^-+/, "") // Trim - from start of text
    .replace(/-+$/, ""); // Trim - from end of text
};

type Field<T extends keyof DB, F extends keyof DB[T] & string> = `data.${F}` | "id" | "operation" | "repeat";

export interface FieldProps<T extends keyof DB, F extends keyof DB[T] & string> {
  table: T;
  field: Field<T, F>;
  formIdentifier?: string;
  hiddenType?: FieldType;
  index?: number | string;
  nameKeys?: Array<number | string>;
  repeat?: boolean;
}

export const joinKeys = (...key: Array<string | number>) =>
  key.map((key) => (typeof key === "number" ? key.toString().padStart(5, "0") : key)).join(".");

export const referencePrefixValue = "reference_prefix-ref-";

const makeRef = (...keys: Array<string | number>) => (referencePrefixValue + joinKeys(...keys)).replace(/\./g, "-");

// The order is important so rows that are referenced by a foreign key are inserted before the row that references it.
const tableKeys: Record<keyof DB, true> = {
  user: true,
  price: true,
  address: true,
  session: true,
  one_time_password: true,
  user_session: true,
  operator: true,
  region: true,
  spot: true,
  intuit_connection: true,
  xendit_platform: true,
  xendit_environment: true,
  xendit_account: true,
  xendit_split_rule: true,
  xendit_virtual_bank_account: true,
  xendit_virtual_bank_account_payment: true,
  establishment: true,
  establishment__language: true,
  form: true,
  field: true,
  boat: true,
  tag: true,
  category: true,
  trip: true,
  member: true,
  booking: true,
  payment: true,
  payment_method: true,
  item: true,
  product: true,
  product_price: true,
  sale_item: true,
  invoice: true,
  invoice_count: true,
  addon: true,
  diving_course: true,
  diving_location: true,
  diving_site: true,
  item__diving_course: true,
  product__diving_location: true,
  product__diving_site: true,
  product__tag: true,
  person: true,
  customer: true,
  participant: true,
  participant_token: true,
  participation: true,
  mail: true,
  activity_addon: true,
  participation_addon: true,
  schedule: true,
  trip_assignment: true,
  tank_assignment: true,
  waiver: true,
  waiver_establishment: true,
  view: true,
  file: true,
  cache: true,
  event: true,
  user_event: true,
  entity_action: true,
  spatial_ref_sys: true,
  currency: true,
  review: true,
  answer_old: true,
  waiver_translation: true,
  form_waiver: true,
  participant_waiver: true,
  participation_waiver: true,
  signature: true,
  // message: true,
  comment: true,
  inquiry: true,
  signup_submission: true,
  callback: true,
  file_target: true,
  session_link: true,
  device_token: true,
  user__device_token: true,
  sortable_value: true,
  rentable: true,
  rental_assignment: true,
  rentable_service: true,
  participant_day: true,
};
export const tableOrder: ReadonlyArray<keyof DB> = keys(tableKeys);

export const booleanRef = (index: number | string = 0) => {
  return makeRef("boolean", index);
};

export const tableIdRef = <T extends keyof DB>(table: T, index: number | string = 0) => {
  const foundTableIndex = tableOrder.indexOf(table);
  const tableIndex = foundTableIndex < 0 ? tableOrder.length : foundTableIndex;
  return makeRef(tableIndex, index, table);
};

export const fName = <T extends keyof DB, F extends keyof DB[T] & string>(
  table: T,
  key: Field<T, F>,
  identifier: number | string = 0,
  ...keys: Array<string | number>
) => joinKeys(tableIdRef(table, identifier), table, key, ...keys);

export const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
export const timeRegex = /^([01][0-9]|2[0-3]):[0-5][0-9]$/;

type ObjectWithoutKey<T, K extends keyof T> = {
  [P in Exclude<keyof T, K>]: T[P];
};

type ObjectWithtKey<T, K extends keyof T> = {
  [P in Exclude<keyof T, K>]: T[P];
};

export function removeObjectKeys<T, K extends keyof T>(originalObject: T, ...keysToRemove: K[]): ObjectWithoutKey<T, K> {
  const newObject = { ...originalObject };
  keysToRemove.forEach((key) => {
    delete newObject[key];
  });
  return newObject as ObjectWithoutKey<T, K>;
}

export function pickObjectKeys<T extends object, K extends keyof T>(originalObject: T, ...keysToPick: K[]): Pick<T, K> {
  const newObject = { ...originalObject };
  Object.keys(newObject).forEach((key) => {
    // @ts-ignore
    if (!keysToPick.includes(key)) {
      // @ts-ignore
      delete newObject[key];
    }
  });
  return newObject;
}

export const urlFromSegments = (...segments: string[]) =>
  flat(segments.map((segment) => (segment.startsWith("http") ? segment : segment.split("/"))))
    .filter((segment) => !!segment)
    .join("/");

export const getFullUrl = (host: string | null) => {
  const hostFallback = host || localHost;
  return hostFallback.endsWith(localHost) ? "http://" + host : "https://" + host;
};

export const mergeObjects = <T extends object>(objects: T[]): T =>
  objects.reduce((acc, item) => {
    if (!acc) return item;
    const newObject = { ...acc };
    Object.entries(item).forEach(([key, value]) => {
      if (value) {
        // @ts-ignore
        newObject[key] = value;
      }
    });
    return newObject;
  }, objects[0]!);

export const isUuidRegex = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i;

export const logTime = (text: string, enabled = true): { end: () => void; timeLog: (data: any) => void } => {
  const shortId = Date.now().toString(32) + Math.random().toString(32).slice(2, 4);
  const logKey = shortId + " - " + text;

  if (!enabled) {
    return {
      timeLog: () => {},
      end: () => {},
    };
  }

  console.time(logKey);
  return {
    timeLog: (data: any) => console.timeLog(logKey, data),
    end: () => console.timeEnd(logKey),
  };
};

// export const remobla = <T>(obj: T) => {
//   if (!obj) return obj;
//   if (Array.isArray(obj)) {
//     return obj.map(obj => {})
//   }
//   if (typeof obj === 'object')
// }

export function removeKeyRecursively<T, K extends string>(obj: T, objKey: K): DeepPartialWithoutKey<T, K> {
  // if (!objKey) return obj;
  // Base case: if the input is not an object or is null, return it directly.
  if (typeof obj !== "object" || !obj) {
    // Handle arrays: recursively call removeId on each element.
    // if (Array.isArray(obj)) {
    //   // Need 'any' here because mapping might change the type structure slightly
    //   // in ways TS struggles with in this recursive context, but the final
    //   // type assertion ensures correctness.
    //   return (obj as any).map((item: any) => removeIdRecursively(item)) as any;
    // }
    return obj as DeepPartialWithoutKey<T, K>; // Type assertion for non-object/non-array base cases
  }

  // Handle arrays specifically (needed because typeof array === 'object')
  if (Array.isArray(obj)) {
    return obj.map((item) => removeKeyRecursively(item, objKey)) as any; // Use 'any' temporarily
  }

  // Create a new object to store the result. Use `any` to avoid type errors
  // during the construction of the new object. The final return will
  // have the correct type.
  const result: any = {};

  // Iterate over the keys of the input object.
  for (const key in obj) {
    // Ensure we are iterating over own properties
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (key !== "id") {
        // Recursive step: if the value is an object (or array), recursively call removeId.
        // Otherwise, copy the value directly.
        const value = obj[key];
        // Check for object/array before recursing
        result[key] = removeKeyRecursively(value, objKey);
      }
      // If the key *is* 'id', we simply skip it, effectively removing it from the result.
    }
  }
  // Use a type assertion here to ensure the correct return type.
  return result as DeepPartialWithoutKey<T, K>;
}

/**
 * A utility type that recursively makes the 'id' property optional
 * in an object and its nested objects/arrays, while preserving the
 * optionality/required status of other properties.
 */
// type DeepPartialWithoutId<T> = T extends (infer E)[] // Handle arrays
//   ? DeepPartialWithoutId<E>[] // Recursively apply to array elements
//   : T extends object // Handle objects
//     ? { [K in keyof Omit<T, "id">]: DeepPartialWithoutId<T[K]> } & { id?: T extends { id: infer IdType } ? IdType : never } // Recursively apply to other properties, preserving optionality // Make 'id' optional, preserving its original type if it existed
//     : T; // Base case: return non-object/non-array types as is

type DeepPartialWithoutKey<T, K extends string> = T extends (infer E)[] // Handle arrays
  ? DeepPartialWithoutKey<E, K>[] // Recursively apply to array elements
  : T extends object // Handle objects
    ? { [P in keyof Omit<T, K>]: DeepPartialWithoutKey<T[P], K> } & { [P in K]?: T extends { [key in P]: infer KeyType } ? KeyType : never } // Recursively apply to other properties, preserving optionality // Make the specified key optional, preserving its original type if it existed
    : T; // Base case: return non-object/non-array types as is

/**
 * Returns the symmetric difference between two arrays.
 * This includes elements that are in either array but not in both.
 */
export function symmetricDifference<T>(array1: T[], array2: T[]): T[] {
  const uniqueArray1 = unique(array1);
  const uniqueArray2 = unique(array2);

  // Elements in array1 that are not in array2
  const diff1 = difference(uniqueArray1, uniqueArray2);
  // Elements in array2 that are not in array1
  const diff2 = difference(uniqueArray2, uniqueArray1);

  // Combine both differences
  return [...diff1, ...diff2];
}
