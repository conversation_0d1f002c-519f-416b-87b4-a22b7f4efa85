import React, { Fragment, ReactNode } from "react";
import { NodeType, Schema } from "@markdoc/markdoc";
import { fName } from "~/misc/helpers";
import { useWaiverContext } from "~/domain/waiver/waiver-context";
import { twMerge } from "tailwind-merge";

const formatStringToCamelCase = (str: string) => {
  const splitted = str.split("-");
  if (splitted.length === 1) return splitted[0] || "";
  return (
    splitted[0] +
      splitted
        .slice(1)
        .map((word) => (word[0]?.toUpperCase() || "") + word.slice(1))
        .join("") || ""
  );
};

export const getStyleObjectFromString = (str: string) => {
  const style: Record<string, string> = {};
  str.split(";").forEach((el) => {
    const [property, value] = el.split(":");
    if (!property) return;

    const formattedProperty = formatStringToCamelCase(property.trim());
    style[formattedProperty] = value!.trim();
  });
  return style;
};

export const MarkDocInput = (props: {
  value?: string;
  defaultValue?: string;
  name?: string;
  type?: string;
  disabled?: boolean;
  placeholder?: string;
  required?: boolean;
  className?: string;
  style?: string;
}) => {
  const waiverCtx = useWaiverContext();
  const type = props.type || "text";
  const disabled = props.disabled || (type === "text" && !!props.value);
  const defaultValue = props.defaultValue || waiverCtx.input?.[props.name || ""];
  const defaultChecked = props.type === "radio" || props.type === "checkbox" ? defaultValue === props.value : undefined;
  return (
    <input
      type={type}
      className={twMerge(
        type === "text" && "input inline-block w-72",
        type === "radio" && "radio",
        type === "checkbox" && "checkbox",
        props.className,
      )}
      style={props.style ? getStyleObjectFromString(props.style) : undefined}
      defaultValue={defaultValue}
      defaultChecked={defaultChecked}
      name={props.name && fName("participant_waiver", "data.input", 0, props.name)}
      placeholder={props.placeholder}
      value={props.value}
      required={props.required}
      disabled={disabled}
    />
  );
};

export const MarkDocLabel = (props: { className?: string; style: string; children: ReactNode }) => {
  return (
    <label className={props.className} style={props.style ? getStyleObjectFromString(props.style) : undefined}>
      {props.children}
    </label>
  );
};

export const MarkDocDiv = (props: { className?: string; style: string; children: ReactNode }) => {
  return (
    <div className={props.className} style={props.style ? getStyleObjectFromString(props.style) : undefined}>
      {props.children}
    </div>
  );
};

export const MarkdocFragment = () => <Fragment />;
export const MarkdocNotImplemented = () => <div>Not implemented</div>;

export const MarkdocBr = () => <br />;

export const MarkDocU = (props: { children: ReactNode }) => {
  return <span className="underline ">{props.children}</span>;
};

export const MarkDocLink = (props: { children: ReactNode; href: string; title?: string }) => {
  return (
    <a className="link" target={"_blank"} href={props.href} title={props.title}>
      {props.children}
    </a>
  );
};

export const MarkdocParagraph = (props: { children: ReactNode }) => {
  return <p className="text-slate-600 mt-3">{props.children}</p>;
};

export const MarkdocHr = (props: {}) => {
  return <hr className="mt-3 text-slate-600" />;
};

export const MarkdocList = (props: { ordered?: boolean; children?: ReactNode }) => {
  return props.ordered ? <ol className="[&_li]:list-decimal">{props.children}</ol> : <ul className="[&_li]:list-disc">{props.children}</ul>;
};

export const MarkdocItem = (props: { ordered?: boolean; children?: ReactNode }) => {
  return <li className="pl-3 list-inside">{props.children}</li>;
};

export const MarkdocHeading = (props: { children: ReactNode; level: number }) => {
  const levels = [
    <h1 className="text-2xl text-slate-600 font-bold">{props.children}</h1>,
    <h2 className="text-xl text-slate-600 font-semibold">{props.children}</h2>,
    <h3 className="text-slate-600 font-semibold">{props.children}</h3>,
    <h4>{props.children}</h4>,
    <h5>{props.children}</h5>,
    <h6>{props.children}</h6>,
  ];
  return levels[props.level - 1] || props.children;
};

export const fallbackNodes = {
  "medical-questions": { render: "MedicalQuestions", selfClosing: true },
  signature: { render: "Signature", selfClosing: true },
  "medical-evaluation": { render: "MedicalEvaluation", selfClosing: true },
} satisfies Record<string, Schema>;

export const markDocNodes = {
  // node: {
  //   transform: (node, config) => {
  //     console.log("node", node);
  //     if (node.type === "text") {
  //       const text = node.attributes.content;
  //
  //       // Regular expression to find URLs
  //       const urlRegex = /(https?:\/\/[^\s]+)/g;
  //
  //       // If the text contains a URL, split it and wrap URLs in <a>
  //       if (urlRegex.test(text)) {
  //         const parts = text.split(urlRegex);
  //
  //         return parts.map((part) => {
  //           if (urlRegex.test(part)) {
  //             return {
  //               type: "a",
  //               attributes: { href: part, target: "_blank", rel: "noopener noreferrer" },
  //               children: [{ type: "text", attributes: { content: part } }],
  //             };
  //           }
  //           return { type: "text", attributes: { content: part } };
  //         });
  //       }
  //     }
  //     return node;
  //   },
  // },
  link: {
    render: "Link",
    // comp: MarkDocLink,
    attributes: { href: { type: "String" } },
  },
  heading: {
    render: "Heading",
    // comp: MarkdocHeading,
    attributes: { level: { type: "Number" } },
  },
  paragraph: {
    render: "Paragraph",
    // comp: MarkdocParagraph,
  },
  hr: {
    render: "Hr",
    // comp: MarkdocHr,
  },
  list: {
    render: "List",
    // comp: MarkdocList,
    attributes: { ordered: { type: "Boolean" } },
  },
  item: {
    render: "Item",
    // comp: MarkdocItem,
  },
} satisfies Partial<Record<NodeType, Schema>>;

export const markdocTags = {
  u: {
    render: "MarkDocU",
  },
  div: {
    render: "MarkDocDiv",
    attributes: { className: { type: "String" }, style: { type: "String" } },
  },
  label: {
    render: "MarkDocLabel",
    attributes: { className: { type: "String" }, style: { type: "String" } },
  },
  br: {
    selfClosing: true,
    render: "MarkDocBr",
  },
  input: {
    render: "MarkDocInput",
    selfClosing: true,
    attributes: {
      value: { type: "String" },
      name: { type: "String" },
      placeholder: { type: "String" },
      defaultValue: { type: "String" },
      type: { type: "String" },
      style: { type: "String" },
      disabled: { type: "Boolean" },
      required: { type: "Boolean" },
    },
  },
  ...fallbackNodes,
} satisfies Record<string, Schema>;

const MedicalQuestionsFallbackComp = () => {
  return <div className="py-3 text-rose-500">-- medical questions component placeholder --</div>;
};

const MedicalExaminerEvaluationFallbackComp = () => {
  return <div className="py-3 text-rose-500">-- Medical examiner's evaluation form placeholder --</div>;
};

const SignatureFallbackComp = () => {
  return <div className="py-3 text-rose-500">-- signature component placeholder --</div>;
};

export const baseMarkdocComps = {
  MarkDocU: MarkDocU,
  MarkDocDiv: MarkDocDiv,
  MarkDocLabel: MarkDocLabel,
  MarkDocInput: MarkDocInput,
  MarkDocBr: MarkdocBr,
  Link: MarkDocLink,
  Heading: MarkdocHeading,
  Paragraph: MarkdocParagraph,
  Hr: MarkdocHr,
  List: MarkdocList,
  Item: MarkdocItem,
};

export const allMarkdocComps = {
  ...baseMarkdocComps,
  MedicalQuestions: MedicalQuestionsFallbackComp,
  Signature: SignatureFallbackComp,
  MedicalEvaluation: MedicalExaminerEvaluationFallbackComp,
};

export type MarkdocComps = Record<keyof typeof allMarkdocComps, any>;
